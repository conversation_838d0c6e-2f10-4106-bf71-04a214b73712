import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { HeroImage } from '@/models/HeroImage';

// GET /api/hero-images/active - Get active hero image (no authentication required)
export async function GET(request) {
  try {
    await connectDB();

    const activeHeroImage = await HeroImage.findOne({ isActive: true });

    if (!activeHeroImage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'No active hero image found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: activeHeroImage,
    });
  } catch (error) {
    console.error('Error fetching active hero image:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch active hero image',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
