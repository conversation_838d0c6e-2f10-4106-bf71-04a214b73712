import mongoose from 'mongoose';
const { Schema } = mongoose;

const HeroImageSchema = new Schema({
    name: { type: String, required: true },
    urls: { type: [String], required: true, validate: [arrayLimit, 'Exceeds the limit of 10 images'] },
    fullPath: { type: String, default: '' },
    contentType: { type: String, default: 'image/jpeg' },
    size: { type: Number, default: 0 },
    uploadedAt: { type: String, default: '' },
    isActive: { type: Boolean, default: false }
}, { timestamps: true });

// Validator function to limit array size
function arrayLimit(val) {
    return val.length <= 10;
}

export const HeroImage = mongoose.models.HeroImage || mongoose.model('HeroImage', HeroImageSchema);
