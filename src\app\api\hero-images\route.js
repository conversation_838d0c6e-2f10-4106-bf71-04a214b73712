import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { HeroImage } from '@/models/HeroImage';

// GET /api/hero-images - Get all hero images with search and filtering (no authentication required)
export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';

    // Build query
    const query = {};

    // Search by name
    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }

    // Filter by active status
    if (activeOnly) {
      query.isActive = true;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await HeroImage.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await HeroImage.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching hero images:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch hero images',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/hero-images - Create new hero image (no authentication required)
export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'url'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }

    // If setting as active, deactivate all other images first
    if (body.isActive) {
      await HeroImage.updateMany({}, { isActive: false });
    }

    // Create new hero image
    const newHeroImage = new HeroImage(body);
    await newHeroImage.save();

    return NextResponse.json(
      {
        success: true,
        data: newHeroImage,
        message: 'Hero image created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating hero image:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create hero image',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/hero-images - Bulk update hero images (no authentication required)
export async function PUT(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { items, action } = body;

    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }

    const results = [];

    for (const itemData of items) {
      try {
        const { _id, ...updateData } = itemData;

        // Handle bulk actions
        if (action) {
          switch (action) {
            case 'activate':
              // Deactivate all others first
              await HeroImage.updateMany({}, { isActive: false });
              updateData.isActive = true;
              break;
            case 'deactivate':
              updateData.isActive = false;
              break;
          }
        }

        // If setting as active, deactivate all other images first
        if (updateData.isActive) {
          await HeroImage.updateMany({}, { isActive: false });
        }

        const updatedHeroImage = await HeroImage.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );

        if (updatedHeroImage) {
          results.push({
            id: _id,
            success: true,
            data: updatedHeroImage,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Hero image not found',
          });
        }
      } catch (error) {
        results.push({
          id: itemData._id,
          success: false,
          error: error.message,
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating hero images:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update hero images',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/hero-images - Bulk delete hero images (no authentication required)
export async function DELETE(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { ids } = body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'ids must be a non-empty array',
        },
        { status: 400 }
      );
    }

    const result = await HeroImage.deleteMany({
      _id: { $in: ids }
    });

    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        requestedCount: ids.length,
      },
      message: `${result.deletedCount} hero images deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting hero images:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete hero images',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
