'use client'
import Link from 'next/link';
import { useSearchParams } from 'next/navigation'
import React, { useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience';
import ImageWrapperResponsive from './ImageWrapperResponsive';
import _360BookNowBtn from '../components/360s/_360BookNowBtn';
import { motion } from 'framer-motion';
import { HiOutlineMenuAlt3, HiX } from 'react-icons/hi';

export default function Navbar() {
    const searchParams = useSearchParams()
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const { experienceState } = useContextExperience()
    const id = searchParams.get('id')
    const links=['the island','experiences','testimonials','location & contacts']

    // console.log('_360Navbar:',experienceState)

  return (
    <motion.nav 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className='navbar flex absolute z-10 top-0 left-0 w-full h-40 items-start from-black bg-gradient-to-b'
    >
      <div className='flex relative w-full top-0 h-20 items-center justify-between'>
        <div className='flex items-center gap-10'>
          <Link href={'/'} className="flex bg-inherit object-left-top relative w-fit h-full text-lg tracking-[6px]">
            <ImageWrapperResponsive className={'w-auto h-full'} src={'/assets/elephant_island_logo_white_for_nav_bar.png'} alt='elephant island logo'/>
          </Link>
          <div className='hidden md:flex text-sm text-center uppercase text-white gap-5 items-center'>
            {links.map(i=><Link href={`/${i}`} key={i}>{i}</Link>)}
          </div>
        </div>
        <div className='flex items-center w-fit h-full'>
          <_360BookNowBtn/>
        </div>
      </div>
      
      {/* Mobile Menu Button */}
      <button
        className="md:hidden text-xl"
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        aria-label="Toggle menu"
      >
        {mobileMenuOpen ? <HiX /> : <HiOutlineMenuAlt3 />}
      </button>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="absolute top-full left-0 w-full bg-white text-neutral-900 shadow-sm py-6 md:hidden">
          <div className="flex flex-col space-y-6 px-6">
            {links.map((link, index) => (
              <Link
                key={index}
                href={`/#${link.href}`}
                className="text-xs tracking-widest font-extralight py-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                {link.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </motion.nav>
  )
}
