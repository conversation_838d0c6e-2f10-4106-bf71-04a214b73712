'use client';

import { useState, useEffect } from 'react';
import { MdAdd, MdSearch, MdRefresh } from 'react-icons/md';
import HeroImageForm from './HeroImageForm';
import HeroImageList from './HeroImageList';

export default function HeroImageManagement() {
  const [heroImages, setHeroImages] = useState([]);
  const [filteredHeroImages, setFilteredHeroImages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingHeroImage, setEditingHeroImage] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch hero images
  const fetchHeroImages = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/hero-images');
      const result = await response.json();

      if (result.success) {
        setHeroImages(result.data);
        setError('');
      } else {
        setError(result.message || 'Failed to fetch hero images');
      }
    } catch (error) {
      console.error('Error fetching hero images:', error);
      setError('Failed to fetch hero images');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter hero images based on search and active filter
  useEffect(() => {
    let filtered = heroImages;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(heroImage =>
        heroImage.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply active filter
    if (activeFilter === 'active') {
      filtered = filtered.filter(heroImage => heroImage.isActive);
    } else if (activeFilter === 'inactive') {
      filtered = filtered.filter(heroImage => !heroImage.isActive);
    }

    setFilteredHeroImages(filtered);
  }, [heroImages, searchTerm, activeFilter]);

  // Load hero images on component mount
  useEffect(() => {
    fetchHeroImages();
  }, []);

  const handleCreateNew = () => {
    setEditingHeroImage(null);
    setShowForm(true);
  };

  const handleEdit = (heroImage) => {
    setEditingHeroImage(heroImage);
    setShowForm(true);
  };

  const handleSave = async (formData) => {
    try {
      setIsSaving(true);
      setError('');

      const url = editingHeroImage 
        ? `/api/hero-images/${editingHeroImage._id}`
        : '/api/hero-images';
      
      const method = editingHeroImage ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(
          editingHeroImage 
            ? 'Hero image updated successfully' 
            : 'Hero image created successfully'
        );
        setShowForm(false);
        setEditingHeroImage(null);
        await fetchHeroImages();
      } else {
        setError(result.message || 'Failed to save hero image');
      }
    } catch (error) {
      console.error('Error saving hero image:', error);
      setError('Failed to save hero image');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (ids) => {
    try {
      const confirmed = window.confirm(
        `Are you sure you want to delete ${ids.length} hero image(s)?`
      );
      
      if (!confirmed) return;

      setError('');

      const response = await fetch('/api/hero-images', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(`${result.data.deletedCount} hero image(s) deleted successfully`);
        await fetchHeroImages();
      } else {
        setError(result.message || 'Failed to delete hero images');
      }
    } catch (error) {
      console.error('Error deleting hero images:', error);
      setError('Failed to delete hero images');
    }
  };

  const handleBulkAction = async (ids, action) => {
    try {
      setError('');

      const items = ids.map(id => ({ _id: id }));

      const response = await fetch('/api/hero-images', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items, action }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(`Bulk ${action} completed successfully`);
        await fetchHeroImages();
      } else {
        setError(result.message || `Failed to ${action} hero images`);
      }
    } catch (error) {
      console.error(`Error ${action} hero images:`, error);
      setError(`Failed to ${action} hero images`);
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingHeroImage(null);
  };

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Hero Images Management</h1>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchHeroImages}
            className="flex items-center px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            <MdRefresh className="mr-2" />
            Refresh
          </button>
          <button
            onClick={handleCreateNew}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <MdAdd className="mr-2" />
            Add Hero Image
          </button>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <p className="text-red-600">{error}</p>
            <button
              onClick={clearMessages}
              className="text-red-400 hover:text-red-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <p className="text-green-600">{success}</p>
            <button
              onClick={clearMessages}
              className="text-green-400 hover:text-green-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Form */}
      {showForm && (
        <HeroImageForm
          heroImage={editingHeroImage}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isSaving}
        />
      )}

      {/* Filters */}
      {!showForm && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Search */}
            <div className="relative">
              <MdSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search hero images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              <select
                value={activeFilter}
                onChange={(e) => setActiveFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* List */}
      {!showForm && (
        <HeroImageList
          heroImages={filteredHeroImages}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBulkAction={handleBulkAction}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
