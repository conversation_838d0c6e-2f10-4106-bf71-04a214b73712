'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdStar, MdStarBorder } from 'react-icons/md';

export default function HeroImageForm({ 
  heroImage = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    name: '',
    urls: [],
    isActive: false,
  });
  const [errors, setErrors] = useState({});
  const [imageFiles, setImageFiles] = useState([]);
  const [imagePreviews, setImagePreviews] = useState([]);
  const [uploading, setUploading] = useState(false);

  // Initialize form data when heroImage prop changes
  useEffect(() => {
    if (heroImage) {
      setFormData({
        name: heroImage.name || '',
        urls: heroImage.urls || [],
        isActive: heroImage.isActive || false,
      });
      setImagePreviews(heroImage.urls || []);
    } else {
      setFormData({
        name: '',
        urls: [],
        isActive: false,
      });
      setImagePreviews([]);
    }
    setImageFiles([]);
  }, [heroImage]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      // Limit to 10 images total
      const maxImages = 10;
      const currentImageCount = imagePreviews.length;
      const availableSlots = maxImages - currentImageCount;

      if (files.length > availableSlots) {
        setErrors(prev => ({
          ...prev,
          files: `You can only add ${availableSlots} more image(s). Maximum ${maxImages} images allowed.`
        }));
        return;
      }

      setImageFiles(prev => [...prev, ...files]);

      // Create preview URLs for new files
      const newPreviews = files.map(file => URL.createObjectURL(file));
      setImagePreviews(prev => [...prev, ...newPreviews]);

      // Clear file error if it exists
      if (errors.files) {
        setErrors(prev => ({
          ...prev,
          files: ''
        }));
      }
    }
  };

  const removePreview = (index) => {
    // Remove from previews
    setImagePreviews(prev => prev.filter((_, i) => i !== index));

    // If it's a new file (not from existing URLs), remove from imageFiles
    const existingUrlsCount = formData.urls.length;
    if (index >= existingUrlsCount) {
      const fileIndex = index - existingUrlsCount;
      setImageFiles(prev => prev.filter((_, i) => i !== fileIndex));
    } else {
      // Remove from existing URLs in formData
      setFormData(prev => ({
        ...prev,
        urls: prev.urls.filter((_, i) => i !== index)
      }));
    }
  };

  const uploadImages = async () => {
    if (imageFiles.length === 0) return [];

    setUploading(true);
    try {
      const uploadFormData = new FormData();
      imageFiles.forEach(file => {
        uploadFormData.append('files', file);
      });

      const response = await fetch('/api/upload/hero-images', {
        method: 'POST',
        body: uploadFormData,
      });

      const result = await response.json();

      if (result.success && result.data.length > 0) {
        return result.data.map(item => item.url);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Images upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    const totalImages = formData.urls.length + imageFiles.length;
    if (totalImages === 0) {
      newErrors.images = 'At least one image is required';
    }

    if (totalImages > 10) {
      newErrors.images = 'Maximum 10 images allowed';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      let allUrls = [...formData.urls];

      // Upload new images if selected
      if (imageFiles.length > 0) {
        const newUrls = await uploadImages();
        allUrls = [...allUrls, ...newUrls];
      }

      const submitData = {
        ...formData,
        urls: allUrls,
      };

      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save hero image. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {heroImage ? 'Edit Hero Image' : 'Create New Hero Image'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter hero image name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Active Status */}
        <div>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700 flex items-center">
              {formData.isActive ? (
                <MdStar className="text-yellow-500 mr-1" />
              ) : (
                <MdStarBorder className="text-gray-400 mr-1" />
              )}
              Set as Active Hero Image
            </span>
          </label>
          <p className="mt-1 text-sm text-gray-500">
            Only one hero image can be active at a time. Setting this as active will deactivate all other hero images.
          </p>
        </div>

        {/* Images Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Images * (Maximum 10 images)
          </label>

          {/* Image Previews */}
          {imagePreviews.length > 0 && (
            <div className="mb-4">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={preview}
                      alt={`Hero image preview ${index + 1}`}
                      className="w-full h-32 object-cover rounded-md border border-gray-300"
                    />
                    <button
                      type="button"
                      onClick={() => removePreview(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <p className="mt-2 text-sm text-gray-600">
                {imagePreviews.length} image(s) selected
              </p>
            </div>
          )}

          {/* File Input */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <MdCloudUpload className="mr-2" />
              {imagePreviews.length > 0 ? 'Add More Images' : 'Choose Hero Images'}
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageChange}
                className="hidden"
              />
            </label>

            {imageFiles.length > 0 && (
              <span className="text-sm text-gray-600">
                {imageFiles.length} new file(s) selected
              </span>
            )}
          </div>

          {errors.images && (
            <p className="mt-1 text-sm text-red-600">{errors.images}</p>
          )}

          {errors.files && (
            <p className="mt-1 text-sm text-red-600">{errors.files}</p>
          )}

          <p className="mt-1 text-sm text-gray-500">
            Select multiple high-quality images suitable for hero section. Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 15MB per image.
          </p>
        </div>



        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save Hero Image'}
          </button>
        </div>
      </form>
    </div>
  );
}
